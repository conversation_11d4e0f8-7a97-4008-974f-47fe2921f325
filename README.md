# Testpad Automation Project

This project has been successfully converted to use Maven for dependency management and build automation.

## Project Structure

The project now follows the standard Maven directory structure:

```
├── pom.xml                     # Maven configuration file
├── testng.xml                  # TestNG configuration
├── README.md                   # This file
├── src/
│   ├── main/
│   │   ├── java/               # Main source code
│   │   │   ├── CQ/             # Core automation classes
│   │   │   ├── CQlinks/        # Link processing classes
│   │   │   ├── Filehandling/   # File handling utilities
│   │   │   ├── Listeners/      # WebDriver listeners
│   │   │   └── Practice_questions/ # Practice question classes
│   │   └── resources/          # Configuration files
│   │       └── Configuation.properties
│   └── test/
│       └── java/
│           └── CQ/             # Test classes
│               ├── TestNg.java
│               └── Login_page_Alerts.java
└── lib/                        # Legacy JAR files (can be removed)
```

## Dependencies

The project now uses Maven to manage the following dependencies:

- **Selenium WebDriver 4.8.3** - Web automation framework
- **Apache POI 5.2.3** - Excel file processing
- **TestNG 7.7.1** - Testing framework
- **SLF4J 1.7.36** - Logging framework
- **JCommander 1.82** - Command line parsing

## Prerequisites

1. **Java 11 or higher** - The project is configured to use Java 11
2. **Apache Maven 3.6+** - For dependency management and building

### Installing Maven

#### Windows:
1. Download Maven from https://maven.apache.org/download.cgi
2. Extract to a directory (e.g., `C:\Program Files\Apache\maven`)
3. Add Maven's `bin` directory to your PATH environment variable
4. Verify installation: `mvn --version`

#### Alternative - Using Chocolatey:
```bash
choco install maven
```

## Building and Running

### 1. Clean and Compile
```bash
mvn clean compile
```

### 2. Run Tests
```bash
mvn test
```

### 3. Package the Application
```bash
mvn package
```

### 4. Run Specific Main Class
```bash
mvn exec:java -Dexec.mainClass="CQ.idsss"
```

### 5. Run with Dependencies
```bash
mvn compile exec:java -Dexec.mainClass="CQ.idsss" -Dexec.classpathScope=runtime
```

## IDE Integration

### IntelliJ IDEA:
1. Open the project folder
2. IntelliJ will automatically detect the Maven project
3. Click "Import Maven Projects" if prompted
4. Wait for dependency resolution

### Eclipse:
1. File → Import → Existing Maven Projects
2. Browse to the project folder
3. Select the project and import

### VS Code:
1. Install the "Extension Pack for Java"
2. Open the project folder
3. VS Code will automatically detect the Maven project

## Configuration

### WebDriver Setup
Make sure you have ChromeDriver installed and accessible in your PATH, or update the WebDriver initialization in your code to specify the driver path.

### File Paths
Update the file paths in your configuration files and Java classes to match your environment:
- `INPUT_FILE_PATH` in `idsss.java`
- `OUTPUT_FILE_PATH` in `idsss.java`
- Any other hardcoded paths

## Migration Notes

### What Changed:
1. **Dependencies**: All JAR files in `lib/` folder are now managed by Maven
2. **Structure**: Source code moved to Maven standard directories
3. **Build**: Now uses Maven for compilation and packaging
4. **Tests**: Test classes moved to `src/test/java/`

### What to Remove:
After verifying everything works with Maven, you can safely delete:
- The `lib/` folder (contains manual JAR dependencies)
- The original `src/` folder structure (backup recommended)
- Any IDE-specific files (`.iml`, `.project`, etc.)

## Troubleshooting

### Common Issues:

1. **Maven not found**: Install Maven and add to PATH
2. **Java version mismatch**: Ensure Java 11+ is installed and JAVA_HOME is set
3. **Dependency resolution fails**: Check internet connection and Maven repository access
4. **Tests fail**: Verify ChromeDriver is installed and accessible

### Useful Maven Commands:

```bash
# Show dependency tree
mvn dependency:tree

# Download sources and javadocs
mvn dependency:sources dependency:resolve -Dclassifier=javadoc

# Skip tests during build
mvn package -DskipTests

# Run specific test class
mvn test -Dtest=TestNg

# Clean workspace
mvn clean
```

## Next Steps

1. Install Maven if not already installed
2. Run `mvn clean compile` to verify the setup
3. Run `mvn test` to execute the test suite
4. Update any hardcoded file paths in your code
5. Consider removing the old `lib/` folder after verification

## Support

If you encounter any issues with the Maven setup, check:
1. Maven installation and PATH configuration
2. Java version compatibility
3. Internet connectivity for dependency downloads
4. Project structure matches Maven conventions
