package CQ;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * Systematic Testpad ID Extractor
 * Extracts question IDs and details from Testpad platform
 */
public class TestpadIdExtractor {

    // ==================== CONFIGURATION CONSTANTS ====================
    private static final String PLATFORM_URL = "https://infra.assess.testpad.chitkara.edu.in/";
    private static final String LOGIN_URL = PLATFORM_URL + "login";
    private static final String PREVIEW_URL = PLATFORM_URL + "quest/preview-react/";

    private static final String EMAIL = "<EMAIL>";
    private static final String PASSWORD = "Gurneet@08";

    private static final String INPUT_FILE_PATH = "C:\\Users\\<USER>\\Documents\\Links.xlsx";
    private static final String OUTPUT_FILE_PATH = "C:\\Users\\<USER>\\Documents\\IDs.xlsx";

    // ==================== XPATH CONSTANTS ====================
    private static final String TABLE_ROW_XPATH = "//tr[@class='ant-table-row ant-table-row-level-0']";
    private static final String SCORE_XPATH = "//tr[@class='ant-table-row ant-table-row-level-0']//*[@class=\"ant-table-cell\"][4]";
    private static final String QUESTION_NAME_XPATH = "//*[@class=\"question-name\"]";
    private static final String QUESTION_INFO_XPATH = "//*[@class=\"question_info ql-editor\"]";
    private static final String QUESTION_TYPE_XPATH = "//*[@id=\"question-type-badge\"]";
    private static final String CODING_DROPDOWN_XPATH = "//*[@class=\"container-fluid dashboard-container\"]//button[@class=\"btn dropdown-toggle btn-light\"]";
    private static final String DROPDOWN_OPTIONS_XPATH = "//*[@class=\"dropdown-menu inner show\"]//li";
    private static final String MCQ_OPTIONS_XPATH = "//*[@class=\"label-choose-answer\"]";
    private static final String MCQ_TEXT_XPATH = ".//*[@class=\"mb-0\"]";
    private static final String SUBMIT_CHOICES_XPATH = ".//*[@class=\"submit_choices\"]";
    private static final String MCQ_RESULT_XPATH = ".//*[@class=\"mcq-result-text\"]";

    // ==================== IFRAME SELECTOR ====================
    private static final String PREVIEW_IFRAME_SELECTOR = "iframe[src*='preview-react/68a40ae9c8c6aa24fab0c5c1']";

    // ==================== TIMING CONSTANTS ====================
    private static final int DEFAULT_WAIT_TIME = 2000;
    private static final int LOGIN_WAIT_TIME = 3000;
    private static final int SCROLL_WAIT_TIME = 500;
    private static final int CLICK_WAIT_TIME = 1000;
    private static final int IFRAME_WAIT_TIME = 15;

    // ==================== MAIN METHOD ====================
    public static void main(String[] args) {
        TestpadIdExtractor extractor = new TestpadIdExtractor();
        extractor.run();
    }

    // ==================== MAIN EXECUTION METHOD ====================
    public void run() {
        WebDriver driver = null;
        XSSFWorkbook inputWorkbook = null;

        try {
            System.out.println("=== Starting Testpad ID Extraction ===");

            // Step 1: Initialize WebDriver
            driver = initializeWebDriver();

            // Step 2: Login to platform
            loginToTestpad(driver);

            // Step 3: Read input URLs from Excel
            List<String> urls = readUrlsFromExcel();

            // Step 4: Extract IDs and scores from URLs
            List<QuestionData> extractedQuestions = extractQuestionDataFromUrls(driver, urls);

            // Step 5: Extract question details and save to Excel
            extractQuestionDetailsToExcel(driver, extractedQuestions);

            System.out.println("=== Extraction completed successfully ===");

        } catch (Exception e) {
            System.err.println("Error during execution: " + e.getMessage());
            e.printStackTrace();
        } finally {
            cleanupResources(driver, inputWorkbook);
        }
    }

    // ==================== WEBDRIVER INITIALIZATION ====================
    private WebDriver initializeWebDriver() {
        System.out.println("Initializing WebDriver...");
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--remote-allow-origins=*");
        options.addArguments("--disable-blink-features=AutomationControlled");
        options.addArguments("--disable-extensions");
        options.addArguments("--no-sandbox");

        WebDriver driver = new ChromeDriver(options);
        driver.manage().window().maximize();
        System.out.println("WebDriver initialized successfully");
        return driver;
    }

    // ==================== LOGIN FUNCTIONALITY ====================
    private void loginToTestpad(WebDriver driver) {
        try {
            System.out.println("Logging into Testpad platform...");
            driver.get(LOGIN_URL);
            Thread.sleep(DEFAULT_WAIT_TIME);

            driver.switchTo().frame("loginIframe");

            WebElement emailField = driver.findElement(By.xpath("//input[@id='email']"));
            emailField.clear();
            emailField.sendKeys(EMAIL);

            WebElement passwordField = driver.findElement(By.id("password"));
            passwordField.clear();
            passwordField.sendKeys(PASSWORD);

            driver.findElement(By.id("submit")).click();
            Thread.sleep(LOGIN_WAIT_TIME);
            driver.switchTo().defaultContent();

            if (driver.getCurrentUrl().contains("login")) {
                throw new RuntimeException("Login failed - still on login page");
            }

            System.out.println("Login successful");
        } catch (Exception e) {
            throw new RuntimeException("Failed to login: " + e.getMessage(), e);
        }
    }

    // ==================== EXCEL READING FUNCTIONALITY ====================
    private List<String> readUrlsFromExcel() throws Exception {
        System.out.println("Reading URLs from Excel file...");
        List<String> urls = new ArrayList<>();

        try (FileInputStream fileInputStream = new FileInputStream(INPUT_FILE_PATH);
             XSSFWorkbook workbook = new XSSFWorkbook(fileInputStream)) {

            XSSFSheet sheet = workbook.getSheetAt(0);
            int rowCount = sheet.getLastRowNum();

            for (int i = 0; i <= rowCount; i++) {
                XSSFRow row = sheet.getRow(i);
                if (row != null) {
                    Cell cell = row.getCell(0);
                    if (cell != null) {
                        String url = getCellValueAsString(cell);
                        if (url != null && !url.trim().isEmpty()) {
                            urls.add(url);
                        }
                    }
                }
            }
        }

        System.out.println("Found " + urls.size() + " URLs to process");
        return urls;
    }

    // ==================== ID AND SCORE EXTRACTION FUNCTIONALITY ====================
    private List<QuestionData> extractQuestionDataFromUrls(WebDriver driver, List<String> urls) throws Exception {
        System.out.println("Extracting IDs and scores from URLs...");
        List<QuestionData> allQuestions = new ArrayList<>();

        for (String url : urls) {
            System.out.println("Processing URL: " + url);
            List<QuestionData> urlQuestions = processUrl(driver, url);
            allQuestions.addAll(urlQuestions);
        }

        System.out.println("Total questions extracted: " + allQuestions.size());
        return allQuestions;
    }

    private List<QuestionData> processUrl(WebDriver driver, String url) throws Exception {
        List<QuestionData> questions = new ArrayList<>();

        driver.get(url);
        Thread.sleep(DEFAULT_WAIT_TIME);

        List<WebElement> tableRows = driver.findElements(By.xpath(TABLE_ROW_XPATH));
        System.out.println("Found " + tableRows.size() + " table rows");

        if (tableRows.size() > 0) {
            clickTableRowsInReverseOrder(driver, tableRows);
            questions = extractQuestionDataFromTable(driver);
        }

        return questions;
    }

    private void clickTableRowsInReverseOrder(WebDriver driver, List<WebElement> tableRows) throws Exception {
        JavascriptExecutor jsExecutor = (JavascriptExecutor) driver;

        for (int i = tableRows.size() - 1; i >= 0; i--) {
            try {
                WebElement currentRow = tableRows.get(i);
                jsExecutor.executeScript("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", currentRow);
                Thread.sleep(SCROLL_WAIT_TIME);

                if (currentRow.isDisplayed() && currentRow.isEnabled()) {
                    try {
                        currentRow.click();
                    } catch (Exception e) {
                        jsExecutor.executeScript("arguments[0].click();", currentRow);
                    }
                    Thread.sleep(CLICK_WAIT_TIME);
                }
            } catch (Exception e) {
                System.err.println("Error clicking row " + (i + 1) + ": " + e.getMessage());
            }
        }
    }

    private List<QuestionData> extractQuestionDataFromTable(WebDriver driver) throws Exception {
        List<QuestionData> questions = new ArrayList<>();
        Thread.sleep(DEFAULT_WAIT_TIME);

        List<WebElement> updatedTableRows = driver.findElements(By.xpath(TABLE_ROW_XPATH));
        List<WebElement> scoreElements = driver.findElements(By.xpath(SCORE_XPATH));

        System.out.println("Found " + updatedTableRows.size() + " rows and " + scoreElements.size() + " score elements");

        for (int i = 0; i < updatedTableRows.size(); i++) {
            try {
                WebElement row = updatedTableRows.get(i);
                String dataRowKey = row.getAttribute("data-row-key");
                String score = "";

                // Extract score from corresponding score element
                if (i < scoreElements.size()) {
                    try {
                        score = scoreElements.get(i).getText().trim();
                    } catch (Exception e) {
                        System.err.println("Error extracting score for row " + (i + 1) + ": " + e.getMessage());
                        score = "N/A";
                    }
                }

                if (dataRowKey != null && !dataRowKey.isEmpty()) {
                    QuestionData questionData = new QuestionData(dataRowKey, score);
                    questions.add(questionData);
                    System.out.println("Extracted ID: " + dataRowKey + ", Score: " + score);
                }
            } catch (Exception e) {
                System.err.println("Error extracting data from row " + (i + 1) + ": " + e.getMessage());
            }
        }

        return questions;
    }

    // ==================== QUESTION DETAILS EXTRACTION ====================
    private void extractQuestionDetailsToExcel(WebDriver driver, List<QuestionData> extractedQuestions) throws Exception {
        System.out.println("Extracting question details to Excel...");

        try (FileOutputStream outputStream = new FileOutputStream(OUTPUT_FILE_PATH);
             XSSFWorkbook workbook = new XSSFWorkbook()) {

            XSSFSheet sheet = workbook.createSheet("Question Details");
            createExcelHeaders(sheet);

            int rowIndex = 1;
            for (QuestionData questionData : extractedQuestions) {
                System.out.println("Processing question ID: " + questionData.id + " with score: " + questionData.score);

                if (navigateToQuestionPreview(driver, questionData.id)) {
                    QuestionDetails details = extractQuestionDetails(driver);
                    writeQuestionToExcel(sheet, rowIndex++, questionData, details);
                } else {
                    System.out.println("Warning: Could not access preview for ID: " + questionData.id);
                    // Still write basic data even if preview fails
                    writeBasicQuestionData(sheet, rowIndex++, questionData);
                }
            }

            workbook.write(outputStream);
            System.out.println("Question details saved to: " + OUTPUT_FILE_PATH);
        }
    }

    private void createExcelHeaders(XSSFSheet sheet) {
        XSSFRow headerRow = sheet.createRow(0);
        int cellIndex = 0;

        headerRow.createCell(cellIndex++).setCellValue("Question ID");
        headerRow.createCell(cellIndex++).setCellValue("Score");
        headerRow.createCell(cellIndex++).setCellValue("Question Name");
        headerRow.createCell(cellIndex++).setCellValue("Question Type");
        headerRow.createCell(cellIndex++).setCellValue("Status");
        headerRow.createCell(cellIndex++).setCellValue("Preview URL");
        headerRow.createCell(cellIndex++).setCellValue("Description");

        // Add headers for MCQ options (assuming max 6 options)
        headerRow.createCell(cellIndex++).setCellValue("Option A");
        headerRow.createCell(cellIndex++).setCellValue("Option B");
        headerRow.createCell(cellIndex++).setCellValue("Option C");
        headerRow.createCell(cellIndex++).setCellValue("Option D");
        headerRow.createCell(cellIndex++).setCellValue("Option E");
        headerRow.createCell(cellIndex++).setCellValue("Option F");
        headerRow.createCell(cellIndex++).setCellValue("Correct Answer");
        headerRow.createCell(cellIndex++).setCellValue("Additional Info");
    }

    private boolean navigateToQuestionPreview(WebDriver driver, String questionId) {
        try {
            driver.get(PREVIEW_URL + questionId);

            if (driver.getCurrentUrl().contains("preview")) {

                return true;
            }
            return false;
        } catch (Exception e) {
            System.err.println("Error navigating to preview for ID " + questionId + ": " + e.getMessage());
            return false;
        }
    }

    private QuestionDetails extractQuestionDetails(WebDriver driver) {
        QuestionDetails details = new QuestionDetails();

        try {
            details.name = driver.findElement(By.xpath(QUESTION_NAME_XPATH)).getText();
            details.description = driver.findElement(By.xpath(QUESTION_INFO_XPATH)).getText();
            details.type = driver.findElement(By.xpath(QUESTION_TYPE_XPATH)).getText();

            if ("CODING".equals(details.type.trim())) {
                details.additionalInfo = extractCodingLanguages(driver);
            } else if ("MCQ".equals(details.type.trim())) {
                extractMcqDetails(driver, details);
            }

        } catch (Exception e) {
            System.err.println("Error extracting question details: " + e.getMessage());
        }

        return details;
    }

    private String extractCodingLanguages(WebDriver driver) {
        try {
            driver.findElement(By.xpath(CODING_DROPDOWN_XPATH)).click();
            List<WebElement> languages = driver.findElements(By.xpath(DROPDOWN_OPTIONS_XPATH));

            StringBuilder languageList = new StringBuilder();
            for (WebElement language : languages) {
                if (languageList.length() > 0) languageList.append(", ");
                languageList.append(language.getText());
            }

            return languageList.toString();
        } catch (Exception e) {
            System.err.println("Error extracting coding languages: " + e.getMessage());
            return "Error extracting languages";
        }
    }

    private void extractMcqDetails(WebDriver driver, QuestionDetails details) {
        try {
            List<WebElement> options = driver.findElements(By.xpath(MCQ_OPTIONS_XPATH));
            details.mcqOptions.clear();
            details.correctAnswerIndex = 0;

            for (int i = 0; i < options.size(); i++) {
                String optionText = options.get(i).findElement(By.xpath(MCQ_TEXT_XPATH)).getText();
                details.mcqOptions.add(optionText);

                // Check if this is the correct answer
                options.get(i).click();
                driver.findElement(By.xpath(SUBMIT_CHOICES_XPATH)).click();
                Thread.sleep(CLICK_WAIT_TIME);

                String result = options.get(i).findElement(By.xpath(MCQ_RESULT_XPATH)).getText().trim();
                if ("correct answer".equals(result)) {
                    details.correctAnswerIndex = i + 1;
                }
            }

            System.out.println("Extracted " + details.mcqOptions.size() + " MCQ options, correct answer: " + details.correctAnswerIndex);

        } catch (Exception e) {
            System.err.println("Error extracting MCQ details: " + e.getMessage());
            details.additionalInfo = "Error extracting MCQ details";
        }
    }

    private void writeQuestionToExcel(XSSFSheet sheet, int rowIndex, QuestionData questionData, QuestionDetails details) {
        XSSFRow row = sheet.createRow(rowIndex);
        int cellIndex = 0;

        row.createCell(cellIndex++).setCellValue(questionData.id);
        row.createCell(cellIndex++).setCellValue(questionData.score);
        row.createCell(cellIndex++).setCellValue(details.name);
        row.createCell(cellIndex++).setCellValue(details.type);
        row.createCell(cellIndex++).setCellValue("Locked Question");
        row.createCell(cellIndex++).setCellValue(PLATFORM_URL + "questions/preview/" + questionData.id);
        row.createCell(cellIndex++).setCellValue(details.description);

        // Write MCQ options in separate cells (max 6 options)
        for (int i = 0; i < 6; i++) {
            if (i < details.mcqOptions.size()) {
                row.createCell(cellIndex++).setCellValue(details.mcqOptions.get(i));
            } else {
                row.createCell(cellIndex++).setCellValue(""); // Empty cell for unused options
            }
        }

        // Write correct answer
        if ("MCQ".equals(details.type.trim()) && details.correctAnswerIndex > 0) {
            row.createCell(cellIndex++).setCellValue(details.correctAnswerIndex);
        } else {
            row.createCell(cellIndex++).setCellValue(""); // Empty for non-MCQ questions
        }

        // Write additional info
        row.createCell(cellIndex++).setCellValue(details.additionalInfo);
    }

    private void writeBasicQuestionData(XSSFSheet sheet, int rowIndex, QuestionData questionData) {
        XSSFRow row = sheet.createRow(rowIndex);
        int cellIndex = 0;

        row.createCell(cellIndex++).setCellValue(questionData.id);
        row.createCell(cellIndex++).setCellValue(questionData.score);
        row.createCell(cellIndex++).setCellValue("Preview not accessible");
        row.createCell(cellIndex++).setCellValue("Unknown");
        row.createCell(cellIndex++).setCellValue("Preview failed");
        row.createCell(cellIndex++).setCellValue(PLATFORM_URL + "questions/preview/" + questionData.id);
        row.createCell(cellIndex++).setCellValue("Could not extract description");

        // Empty cells for MCQ options (6 options + correct answer)
        for (int i = 0; i < 7; i++) {
            row.createCell(cellIndex++).setCellValue("");
        }

        row.createCell(cellIndex++).setCellValue("N/A");
    }

    // ==================== UTILITY METHODS ====================
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING: return cell.getStringCellValue().trim();
            case NUMERIC: return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN: return String.valueOf(cell.getBooleanCellValue());
            case FORMULA: return cell.getCellFormula();
            case BLANK: return "";
            default: return cell.toString().trim();
        }
    }

    private void cleanupResources(WebDriver driver, XSSFWorkbook workbook) {
        try {
            if (workbook != null) {
                workbook.close();
                System.out.println("Workbook closed");
            }
            if (driver != null) {
                driver.quit();
                System.out.println("WebDriver closed");
            }
        } catch (Exception e) {
            System.err.println("Error closing resources: " + e.getMessage());
        }
    }

    // ==================== DATA CLASSES ====================
    private static class QuestionData {
        String id = "";
        String score = "";

        QuestionData(String id, String score) {
            this.id = id;
            this.score = score;
        }
    }

    private static class QuestionDetails {
        String name = "";
        String description = "";
        String type = "";
        String additionalInfo = "";
        List<String> mcqOptions = new ArrayList<>();
        int correctAnswerIndex = 0;
    }
}
