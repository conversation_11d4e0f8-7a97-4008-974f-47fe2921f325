package forclasss;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import io.github.bonigarcia.wdm.WebDriverManager;

public class pct {
    public static void main(String[] args) {
        WebDriver web = null;

        try {
            // Setup WebDriverManager to automatically download compatible ChromeDriver
            WebDriverManager.chromedriver().setup();

            web = new ChromeDriver();
            web.get("https://demoqa.com/automation-practice-form");
            web.manage().window().maximize();

            // Wait for 3 seconds to see the page
            Thread.sleep(3000);

        } catch (Exception e) {
            System.err.println("Error occurred: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (web != null) {
                web.quit();
                System.out.println("WebDriver closed successfully");
            }
        }
    }
}
