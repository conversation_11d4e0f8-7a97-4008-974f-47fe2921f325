/*
 * ================================================================================
 * TESTPAD ID EXTRACTOR
 * ================================================================================
 *
 * This application extracts IDs from the Testpad platform using the login and
 * file reading functionality copied from Testpadlinkz.java.
 *
 * Features:
 * - Automated login to Testpad platform
 * - Excel file reading capabilities
 * - Comprehensive error handling and logging
 *
 * Author: [Your Name]
 * Version: 1.0
 * Last Modified: [Current Date]
 *
 * ================================================================================
 */

package CQ;

// ================================================================================
// IMPORTS SECTION
// ================================================================================

// Apache POI imports for Excel file handling
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

// Selenium WebDriver imports for web automation
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;

// WebDriverManager for automatic driver management
import io.github.bonigarcia.wdm.WebDriverManager;

// Java standard library imports
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.List;

/**
 * ================================================================================
 * MAIN CLASS: idsss
 * ================================================================================
 *
 * This class contains the login and file reading functionality copied from
 * Testpadlinkz.java for extracting IDs from the Testpad platform.
 */
public class idsss {

    // ================================================================================
    // CONFIGURATION CONSTANTS
    // ================================================================================

    /** URL for the Testpad login page */
    private static final String LOGIN_URL = "https://infra.assess.testpad.chitkara.edu.in/login";

    /** Email address for authentication */
    private static final String EMAIL = "<EMAIL>";

    /** Password for authentication */
    private static final String PASSWORD = "Gurneet@08";

    /** Path to the input Excel file containing URLs to process */
    private static final String INPUT_FILE_PATH = "C:\\Users\\<USER>\\Documents\\Links.xlsx";

    /** Path to the output Excel file where extracted data will be saved */
    private static final String OUTPUT_FILE_PATH = "C:\\Users\\<USER>\\Documents\\IDs.xlsx";

    /** Base URL for the Testpad platform */
    private static final String BASE_URL = "https://assess.testpad.chitkara.edu.in";

    /**
     * ================================================================================
     * MAIN METHOD - ENTRY POINT
     * ================================================================================
     *
     * Main method that orchestrates the login and file reading process.
     *
     * @param args Command line arguments (not used)
     */
    static public void main(String[] args) {
        WebDriver web = null;
        FileOutputStream outputStream = null;
        XSSFWorkbook inputWorkbook = null;
        XSSFWorkbook outputWorkbook = null;
        int rc = 0;

        try {
            System.out.println("Starting Testpad ID extraction...");

            // Setup WebDriverManager to automatically download compatible ChromeDriver
            System.out.println("Setting up WebDriverManager for Chrome...");

            // Initialize WebDriver with options
            ChromeOptions options = new ChromeOptions();
            options.addArguments("--remote-allow-origins=*");
            options.addArguments("--disable-blink-features=AutomationControlled");
            options.addArguments("--disable-extensions");
            options.addArguments("--no-sandbox");
            options.addArguments("--disable-dev-shm-usage");
            options.addArguments("--disable-web-security");
            options.addArguments("--allow-running-insecure-content");
            options.setExperimentalOption("useAutomationExtension", false);
            options.setExperimentalOption("excludeSwitches", new String[]{"enable-automation"});

            web = new ChromeDriver(options);
            System.out.println("WebDriver initialized with enhanced options and compatible ChromeDriver");

            // Login to the platform
            loginToTestpad(web);
            System.out.println("Successfully logged in");

            // Read input Excel file with URLs
            System.out.println("Reading input file: " + INPUT_FILE_PATH);
            inputWorkbook = readInputExcel(INPUT_FILE_PATH);
            XSSFSheet inputSheet = inputWorkbook.getSheetAt(0);
            int rowCount = inputSheet.getLastRowNum();
            System.out.println("Found " + rowCount + " rows in input file");

            // Create output Excel file
            System.out.println("Creating output file: " + OUTPUT_FILE_PATH);
            outputWorkbook = new XSSFWorkbook();
            outputStream = new FileOutputStream(OUTPUT_FILE_PATH);

            // Create a summary sheet
            XSSFSheet summarySheet = outputWorkbook.createSheet("Summary");
            createSummarySheetHeaders(summarySheet);
            int summaryRowCounter = 1; // Start from row 1 (row 0 is header)

            // Process each URL from input Excel
            for (int i = 0; i <= rowCount; i++) {
                XSSFRow row = inputSheet.getRow(i);
                if (row != null) {
                    Cell cell = row.getCell(0);
                    if (cell != null) {
                        String url = getCellValueAsString(cell);
                        if (url != null && !url.trim().isEmpty()) {
                            System.out.println("Processing URL: " + url);

                        // Create individual sheet for this URL
                        String sheetName = "URL_" + (i + 1);
                        XSSFSheet urlSheet = outputWorkbook.createSheet(sheetName);

                            // Process URL and get results
                            ProcessResult result = processUrlWithSheets(web, url, urlSheet, summarySheet, summaryRowCounter);
                            summaryRowCounter = result.summaryRowCounter;
                        } else {
                            System.out.println("Warning: URL is empty at row " + i + ". Skipping this row.");
                        }
                    } else {
                        System.out.println("Warning: Cell at row " + i + ", column 0 is null. Skipping this row.");
                    }
                } else {
                    System.out.println("Warning: Row " + i + " is null. Skipping this row.");
                }
            }

            // Save and close resources
            System.out.println("Writing output to file...");
            outputWorkbook.write(outputStream);
            System.out.println("ID extraction completed successfully!");

        } catch (Exception e) {
            System.err.println("Error occurred during execution: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Clean up resources
            try {
                if (outputStream != null) {
                    outputStream.close();
                    System.out.println("Output stream closed");
                }
                if (inputWorkbook != null) {
                    inputWorkbook.close();
                    System.out.println("Input workbook closed");
                }
                if (outputWorkbook != null) {
                    outputWorkbook.close();
                    System.out.println("Output workbook closed");
                }
                if (web != null) {
                    web.quit();  // Using quit() instead of close() to ensure all browser windows are closed
                    System.out.println("WebDriver closed");
                }
            } catch (Exception e) {
                System.err.println("Error while closing resources: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * ================================================================================
     * HELPER CLASSES AND DATA STRUCTURES
     * ================================================================================
     */

    /**
     * Class to hold processing results
     */
    private static class ProcessResult {
        int summaryRowCounter;

        ProcessResult(int summaryRowCounter) {
            this.summaryRowCounter = summaryRowCounter;
        }
    }

    /**
     * ================================================================================
     * SHEET MANAGEMENT FUNCTIONALITY
     * ================================================================================
     */

    /**
     * Create headers for the summary sheet
     * @param summarySheet The summary sheet to add headers to
     */
    private static void createSummarySheetHeaders(XSSFSheet summarySheet) {
        XSSFRow headerRow = summarySheet.createRow(0);
        headerRow.createCell(0).setCellValue("URL");
        headerRow.createCell(1).setCellValue("Sheet Name");
        headerRow.createCell(2).setCellValue("Total Rows Found");
        headerRow.createCell(3).setCellValue("Total Keys Extracted");
        headerRow.createCell(4).setCellValue("Processing Status");
        headerRow.createCell(5).setCellValue("Processing Time");

        System.out.println("Summary sheet headers created");
    }

    /**
     * Create headers for individual URL sheets
     * @param urlSheet The URL sheet to add headers to
     */
    private static void createUrlSheetHeaders(XSSFSheet urlSheet) {
        // Create headers for click results
        XSSFRow headerRow1 = urlSheet.createRow(0);
        headerRow1.createCell(0).setCellValue("Click Results");

        // Create headers for data-row-key values
        XSSFRow headerRow2 = urlSheet.createRow(2);
        headerRow2.createCell(0).setCellValue("Row Number");
        headerRow2.createCell(1).setCellValue("Data-Row-Key");
        headerRow2.createCell(2).setCellValue("Extraction Status");
    }

    /**
     * ================================================================================
     * UTILITY METHODS
     * ================================================================================
     */

    /**
     * Extract cell value as string, handling different cell types
     * @param cell The cell to extract value from
     * @return String value of the cell
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return cell.toString().trim();
        }
    }

    /**
     * ================================================================================
     * LOGIN FUNCTIONALITY (COPIED FROM TESTPADLINKZ.JAVA)
     * ================================================================================
     */

    /**
     * Login to the Testpad platform
     * @param web WebDriver instance
     * @throws RuntimeException if login fails
     */
    private static void loginToTestpad(WebDriver web) {
        WebDriverWait wait = new WebDriverWait(web, Duration.ofSeconds(10));

        try {
            System.out.println("Navigating to login page: " + LOGIN_URL);
            web.get(LOGIN_URL);
            web.manage().window().maximize();

            // Wait for the login iframe to be available
            Thread.sleep(2000);

            System.out.println("Switching to login iframe");
            web.switchTo().frame("loginIframe");

            System.out.println("Entering credentials");
            WebElement emailField = web.findElement(By.xpath("//input[@id='email']"));
            emailField.clear();
            emailField.sendKeys(EMAIL);

            WebElement passwordField = web.findElement(By.id("password"));
            passwordField.clear();
            passwordField.sendKeys(PASSWORD);

            System.out.println("Submitting login form");
            web.findElement(By.id("submit")).click();

            // Wait for login to complete
            Thread.sleep(3000);

            // Switch back to default content
            web.switchTo().defaultContent();

            // Verify login was successful
            if (web.getCurrentUrl().contains("login")) {
                throw new RuntimeException("Login failed. Still on login page.");
            }

            System.out.println("Login successful");
        } catch (Exception e) {
            System.err.println("Error during login: " + e.getMessage());
            throw new RuntimeException("Failed to login: " + e.getMessage(), e);
        }
    }

    /**
     * ================================================================================
     * FILE READING FUNCTIONALITY (COPIED FROM TESTPADLINKZ.JAVA)
     * ================================================================================
     */

    /**
     * Read the input Excel file
     * @param filePath Path to the input Excel file
     * @return XSSFWorkbook instance
     * @throws IOException If file cannot be read
     */
    private static XSSFWorkbook readInputExcel(String filePath) throws IOException {
        FileInputStream fileInputStream = null;
        try {
            System.out.println("Opening input file: " + filePath);
            fileInputStream = new FileInputStream(filePath);
            XSSFWorkbook workbook = new XSSFWorkbook(fileInputStream);

            // Verify the workbook has at least one sheet
            if (workbook.getNumberOfSheets() == 0) {
                throw new IOException("Excel file contains no sheets");
            }

            System.out.println("Successfully read Excel file with " + workbook.getNumberOfSheets() + " sheet(s)");
            return workbook;
        } catch (IOException e) {
            System.err.println("Error reading Excel file: " + e.getMessage());
            throw e;
        } finally {
            // Close the input stream if it was opened
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    System.err.println("Error closing file input stream: " + e.getMessage());
                }
            }
        }
    }

    /**
     * ================================================================================
     * URL PROCESSING FUNCTIONALITY WITH SHEETS
     * ================================================================================
     */

    /**
     * Process a URL and organize data in separate sheets
     * @param web WebDriver instance
     * @param url URL to process
     * @param urlSheet Individual sheet for this URL
     * @param summarySheet Summary sheet for all URLs
     * @param summaryRowCounter Current row counter for summary
     * @return ProcessResult with updated counters
     * @throws InterruptedException If thread sleep is interrupted
     */
    private static ProcessResult processUrlWithSheets(WebDriver web, String url, XSSFSheet urlSheet,
                                                     XSSFSheet summarySheet, int summaryRowCounter) throws InterruptedException {
        long startTime = System.currentTimeMillis();
        int clickRowCounter = 3; // Start after headers and URL info - declare at method level

        try {
            System.out.println("Processing URL: " + url);
            web.get(url);
            Thread.sleep(2000); // Wait for page to load completely

            // Create headers for this URL sheet
            createUrlSheetHeaders(urlSheet);

            // Add URL information to the sheet
            XSSFRow urlInfoRow = urlSheet.createRow(1);
            urlInfoRow.createCell(0).setCellValue("URL: " + url);

            // Find table rows with the specified XPath
            String tableRowXPath = "//tr[@class='ant-table-row ant-table-row-level-0']";
            List<WebElement> tableRows = web.findElements(By.xpath(tableRowXPath));

            System.out.println("Found " + tableRows.size() + " table rows with class 'ant-table-row ant-table-row-level-0'");

            if (tableRows.size() > 0) {
                // Click elements in reverse order
                System.out.println("Clicking table rows in reverse order...");

                for (int i = tableRows.size() - 1; i >= 0; i--) {
                    try {
                        WebElement currentRow = tableRows.get(i);

                        // Scroll to element to ensure it's visible
                        JavascriptExecutor jsExecutor = (JavascriptExecutor) web;
                        jsExecutor.executeScript(
                            "arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});",
                            currentRow
                        );

                        Thread.sleep(500); // Wait for scroll to complete

                        // Check if element is clickable
                        if (currentRow.isDisplayed() && currentRow.isEnabled()) {
                            System.out.println("Clicking row " + (i + 1) + " of " + tableRows.size() +
                                             " (reverse order: " + (tableRows.size() - i) + ")");

                            // Try regular click first
                            try {
                                currentRow.click();
                            } catch (Exception clickException) {
                                // If regular click fails, try JavaScript click
                                System.out.println("Regular click failed, trying JavaScript click...");
                                jsExecutor.executeScript("arguments[0].click();", currentRow);
                            }

                            Thread.sleep(1000); // Wait between clicks

                            // Record successful click in URL sheet
                            XSSFRow clickRow = urlSheet.createRow(clickRowCounter++);
                            clickRow.createCell(0).setCellValue("Row " + (i + 1) + " clicked successfully");

                        } else {
                            System.out.println("Row " + (i + 1) + " is not clickable (not displayed or not enabled)");
                            XSSFRow clickRow = urlSheet.createRow(clickRowCounter++);
                            clickRow.createCell(0).setCellValue("Row " + (i + 1) + " not clickable");
                        }

                    } catch (Exception rowException) {
                        System.err.println("Error clicking row " + (i + 1) + ": " + rowException.getMessage());
                        XSSFRow clickRow = urlSheet.createRow(clickRowCounter++);
                        clickRow.createCell(0).setCellValue("Row " + (i + 1) + " error: " + rowException.getMessage());
                    }
                }

                // After clicking all elements, extract data-row-key values
                System.out.println("Extracting data-row-key values from all table rows...");
                Thread.sleep(2000); // Wait for any page updates after clicking

                // Re-find elements to get updated state
                List<WebElement> updatedTableRows = web.findElements(By.xpath(tableRowXPath));
                System.out.println("Found " + updatedTableRows.size() + " table rows for data-row-key extraction");

                // Add separator row in URL sheet
                clickRowCounter++; // Empty row
                XSSFRow separatorRow = urlSheet.createRow(clickRowCounter++);
                separatorRow.createCell(0).setCellValue("=== DATA-ROW-KEY VALUES ===");

                // Start data-row-key section
                int keyRowCounter = clickRowCounter;
                int extractedKeysCount = 0;

                // Extract data-row-key from all elements
                for (int j = 0; j < updatedTableRows.size(); j++) {
                    try {
                        WebElement row = updatedTableRows.get(j);
                        String dataRowKey = row.getAttribute("data-row-key");

                        XSSFRow keyRow = urlSheet.createRow(keyRowCounter++);
                        keyRow.createCell(0).setCellValue(j + 1); // Row number

                        if (dataRowKey != null && !dataRowKey.isEmpty()) {
                            System.out.println("Row " + (j + 1) + " data-row-key: " + dataRowKey);
                            keyRow.createCell(1).setCellValue(dataRowKey);
                            keyRow.createCell(2).setCellValue("Success");
                            extractedKeysCount++;
                        } else {
                            System.out.println("Row " + (j + 1) + " has no data-row-key attribute");
                            keyRow.createCell(1).setCellValue("No data-row-key");
                            keyRow.createCell(2).setCellValue("Missing");
                        }

                    } catch (Exception keyException) {
                        System.err.println("Error extracting data-row-key from row " + (j + 1) + ": " + keyException.getMessage());
                        XSSFRow keyRow = urlSheet.createRow(keyRowCounter++);
                        keyRow.createCell(0).setCellValue(j + 1);
                        keyRow.createCell(1).setCellValue("Error");
                        keyRow.createCell(2).setCellValue(keyException.getMessage());
                    }
                }

                // Add summary to URL sheet
                keyRowCounter++; // Empty row
                XSSFRow summaryRow = urlSheet.createRow(keyRowCounter++);
                summaryRow.createCell(0).setCellValue("SUMMARY");
                summaryRow.createCell(1).setCellValue("Total rows: " + tableRows.size());

                XSSFRow summaryRow2 = urlSheet.createRow(keyRowCounter++);
                summaryRow2.createCell(0).setCellValue("Keys extracted");
                summaryRow2.createCell(1).setCellValue(extractedKeysCount);

                // Update summary sheet
                long endTime = System.currentTimeMillis();
                long processingTime = endTime - startTime;

                XSSFRow summarySheetRow = summarySheet.createRow(summaryRowCounter);
                summarySheetRow.createCell(0).setCellValue(url);
                summarySheetRow.createCell(1).setCellValue(urlSheet.getSheetName());
                summarySheetRow.createCell(2).setCellValue(tableRows.size());
                summarySheetRow.createCell(3).setCellValue(extractedKeysCount);
                summarySheetRow.createCell(4).setCellValue("Success");
                summarySheetRow.createCell(5).setCellValue(processingTime + " ms");

            } else {
                System.out.println("No table rows found with the specified XPath");
                XSSFRow noRowsRow = urlSheet.createRow(clickRowCounter++);
                noRowsRow.createCell(0).setCellValue("No table rows found with specified XPath");

                // Update summary sheet for no rows case
                long endTime = System.currentTimeMillis();
                long processingTime = endTime - startTime;

                XSSFRow summarySheetRow = summarySheet.createRow(summaryRowCounter);
                summarySheetRow.createCell(0).setCellValue(url);
                summarySheetRow.createCell(1).setCellValue(urlSheet.getSheetName());
                summarySheetRow.createCell(2).setCellValue(0);
                summarySheetRow.createCell(3).setCellValue(0);
                summarySheetRow.createCell(4).setCellValue("No rows found");
                summarySheetRow.createCell(5).setCellValue(processingTime + " ms");
            }

            System.out.println("Processed URL successfully, found and processed " + tableRows.size() + " table rows");
            return new ProcessResult(summaryRowCounter + 1);

        } catch (Exception e) {
            System.err.println("Error processing URL: " + url + ". Error: " + e.getMessage());
            e.printStackTrace();

            // Add error information to URL sheet
            XSSFRow errorRow = urlSheet.createRow(clickRowCounter++);
            errorRow.createCell(0).setCellValue("ERROR: " + e.getMessage());

            // Update summary sheet for error case
            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;

            XSSFRow summarySheetRow = summarySheet.createRow(summaryRowCounter);
            summarySheetRow.createCell(0).setCellValue(url);
            summarySheetRow.createCell(1).setCellValue(urlSheet.getSheetName());
            summarySheetRow.createCell(2).setCellValue(0);
            summarySheetRow.createCell(3).setCellValue(0);
            summarySheetRow.createCell(4).setCellValue("ERROR: " + e.getMessage());
            summarySheetRow.createCell(5).setCellValue(processingTime + " ms");

            return new ProcessResult(summaryRowCounter + 1);
        }
    }
}
