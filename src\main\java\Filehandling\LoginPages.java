package Filehandling;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import io.github.bonigarcia.wdm.WebDriverManager;

import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * LoginPages class for handling WebDriver initialization and configuration
 */
public class LoginPages {
    static WebDriver driver;

    public static void main(String[] arg) throws IOException {
        try {
            // Try to load from classpath first (Maven resources)
            InputStream inputStream = LoginPages.class.getClassLoader()
                .getResourceAsStream("Configuation.properties");

            Properties prop = new Properties();

            if (inputStream != null) {
                // Load from classpath (Maven resources)
                prop.load(inputStream);
                System.out.println("Loaded configuration from classpath");
            } else {
                // Fallback to file system (for backward compatibility)
                try {
                    FileReader reader = new FileReader("src/main/resources/Configuation.properties");
                    prop.load(reader);
                    reader.close();
                    System.out.println("Loaded configuration from file system");
                } catch (IOException e) {
                    System.err.println("Could not load configuration file: " + e.getMessage());
                    System.err.println("Using WebDriverManager for automatic driver setup");
                }
            }

            // Use WebDriverManager for automatic driver management
            WebDriverManager.chromedriver().setup();

            driver = new ChromeDriver();
            driver.manage().window().maximize();

            System.out.println("WebDriver initialized successfully");

        } catch (Exception e) {
            System.err.println("Error initializing WebDriver: " + e.getMessage());
            e.printStackTrace();
        }
    }
}