package CQ;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * Utility class for getting HTTP response codes from URLs
 */
public class GetHttpResponse {

    /**
     * Get HTTP response code for a given URL
     * @param url The URL to check
     * @return HTTP response code
     * @throws MalformedURLException if URL is malformed
     * @throws IOException if connection fails
     */
    public int code(String url) throws MalformedURLException, IOException {
        HttpURLConnection connection = null;
        try {
            URL urlObject = new URL(url);
            connection = (HttpURLConnection) urlObject.openConnection();

            // Set reasonable timeout values
            connection.setConnectTimeout(5000); // 5 seconds
            connection.setReadTimeout(10000);   // 10 seconds

            // Set user agent to avoid blocking
            connection.setRequestProperty("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

            return connection.getResponseCode();
        } finally {
            // Properly close the connection
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
}