@echo off
REM Maven Build Script for Testpad Automation Project
REM This script provides easy access to common Maven commands

echo ========================================
echo Testpad Automation Project - Maven Build
echo ========================================
echo.

:menu
echo Please select an option:
echo 1. Clean and Compile
echo 2. Run Tests
echo 3. Package Application
echo 4. Run Main Application (idsss)
echo 5. Show Dependencies
echo 6. Install Maven Dependencies
echo 7. Clean Workspace
echo 8. Exit
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto clean_compile
if "%choice%"=="2" goto run_tests
if "%choice%"=="3" goto package
if "%choice%"=="4" goto run_main
if "%choice%"=="5" goto show_deps
if "%choice%"=="6" goto install_deps
if "%choice%"=="7" goto clean
if "%choice%"=="8" goto exit
echo Invalid choice. Please try again.
goto menu

:clean_compile
echo.
echo Cleaning and compiling the project...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd clean compile
if %errorlevel% neq 0 (
    echo Build failed! Please check the errors above.
    pause
)
goto menu

:run_tests
echo.
echo Running tests...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd test
if %errorlevel% neq 0 (
    echo Tests failed! Please check the errors above.
    pause
)
goto menu

:package
echo.
echo Packaging the application...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd package
if %errorlevel% neq 0 (
    echo Packaging failed! Please check the errors above.
    pause
)
goto menu

:run_main
echo.
echo Running the main application (idsss)...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd compile exec:java -Dexec.mainClass="CQ.idsss" -Dexec.classpathScope=runtime
if %errorlevel% neq 0 (
    echo Application failed to run! Please check the errors above.
    pause
)
goto menu

:show_deps
echo.
echo Showing dependency tree...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd dependency:tree
pause
goto menu

:install_deps
echo.
echo Installing/updating dependencies...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd dependency:resolve
if %errorlevel% neq 0 (
    echo Dependency installation failed! Please check the errors above.
    pause
)
goto menu

:clean
echo.
echo Cleaning workspace...
C:\maven\apache-maven-3.9.11\bin\mvn.cmd clean
if %errorlevel% neq 0 (
    echo Clean failed! Please check the errors above.
    pause
)
goto menu

:exit
echo.
echo Goodbye!
pause
exit
