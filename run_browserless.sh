#!/bin/bash

# ================================================================================
# BROWSERLESS TESTPAD EXTRACTOR RUNNER
# ================================================================================
# 
# This script sets up the environment and runs the Testpad question extractor
# in browserless mode using Xvfb (X Virtual Framebuffer).
# 
# Prerequisites:
# - Java 8 or higher
# - Chrome/Chromium browser
# - ChromeDriver
# - Xvfb (X Virtual Framebuffer)
# 
# Usage:
#   chmod +x run_browserless.sh
#   ./run_browserless.sh
# 
# ================================================================================

# Configuration
DISPLAY_NUM=":99"
SCREEN_RESOLUTION="1920x1080x24"
JAVA_CLASS="CQlinks.browserless"
CLASSPATH="./src:./lib/*"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Java
    if command_exists java; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        print_success "Java found: $JAVA_VERSION"
    else
        print_error "Java not found. Please install Java 8 or higher."
        exit 1
    fi
    
    # Check Chrome/Chromium
    if command_exists google-chrome || command_exists chromium-browser || command_exists chromium; then
        print_success "Chrome/Chromium browser found"
    else
        print_error "Chrome/Chromium browser not found. Please install Chrome or Chromium."
        exit 1
    fi
    
    # Check ChromeDriver
    if command_exists chromedriver; then
        CHROMEDRIVER_VERSION=$(chromedriver --version 2>/dev/null | cut -d' ' -f2)
        print_success "ChromeDriver found: $CHROMEDRIVER_VERSION"
    else
        print_error "ChromeDriver not found. Please install ChromeDriver and add it to PATH."
        exit 1
    fi
    
    # Check Xvfb
    if command_exists Xvfb; then
        print_success "Xvfb found"
    else
        print_error "Xvfb not found. Please install Xvfb:"
        print_error "  Ubuntu/Debian: sudo apt-get install xvfb"
        print_error "  CentOS/RHEL: sudo yum install xorg-x11-server-Xvfb"
        exit 1
    fi
    
    # Check if Java class file exists
    if [ -f "src/CQlinks/browserless.java" ]; then
        print_success "Java source file found"
    else
        print_error "Java source file not found: src/CQlinks/browserless.java"
        exit 1
    fi
}

# Function to compile Java code
compile_java() {
    print_status "Compiling Java code..."
    
    # Create lib directory if it doesn't exist
    mkdir -p lib
    
    # Compile the Java code
    if javac -cp "$CLASSPATH" src/CQlinks/browserless.java; then
        print_success "Java compilation successful"
    else
        print_error "Java compilation failed"
        exit 1
    fi
}

# Function to setup Xvfb
setup_xvfb() {
    print_status "Setting up Xvfb virtual display..."
    
    # Kill any existing Xvfb process on the same display
    pkill -f "Xvfb $DISPLAY_NUM" 2>/dev/null || true
    sleep 1
    
    # Start Xvfb
    print_status "Starting Xvfb on display $DISPLAY_NUM with resolution $SCREEN_RESOLUTION"
    Xvfb $DISPLAY_NUM -screen 0 $SCREEN_RESOLUTION -ac +extension GLX +extension RANDR &
    XVFB_PID=$!
    
    # Wait for Xvfb to start
    sleep 3
    
    # Check if Xvfb is running
    if kill -0 $XVFB_PID 2>/dev/null; then
        print_success "Xvfb started successfully (PID: $XVFB_PID)"
        export DISPLAY=$DISPLAY_NUM
        print_status "DISPLAY environment variable set to $DISPLAY_NUM"
    else
        print_error "Failed to start Xvfb"
        exit 1
    fi
}

# Function to cleanup Xvfb
cleanup_xvfb() {
    print_status "Cleaning up Xvfb..."
    
    if [ ! -z "$XVFB_PID" ] && kill -0 $XVFB_PID 2>/dev/null; then
        kill $XVFB_PID
        print_success "Xvfb process terminated"
    fi
    
    # Also kill any remaining Xvfb processes
    pkill -f "Xvfb $DISPLAY_NUM" 2>/dev/null || true
}

# Function to run the Java application
run_application() {
    print_status "Running Testpad Question Extractor in browserless mode..."
    print_status "This may take several minutes depending on the number of URLs to process..."
    
    # Run the Java application
    java -cp "$CLASSPATH" $JAVA_CLASS
    
    if [ $? -eq 0 ]; then
        print_success "Application completed successfully!"
    else
        print_error "Application failed with exit code $?"
        return 1
    fi
}

# Function to handle script interruption
cleanup_on_exit() {
    print_warning "Script interrupted. Cleaning up..."
    cleanup_xvfb
    exit 1
}

# Main execution
main() {
    print_status "Starting Browserless Testpad Extractor..."
    print_status "========================================"
    
    # Set up signal handlers for cleanup
    trap cleanup_on_exit INT TERM
    
    # Check prerequisites
    check_prerequisites
    
    # Compile Java code
    compile_java
    
    # Setup Xvfb
    setup_xvfb
    
    # Run the application
    if run_application; then
        print_success "========================================"
        print_success "Browserless extraction completed successfully!"
    else
        print_error "========================================"
        print_error "Browserless extraction failed!"
    fi
    
    # Cleanup
    cleanup_xvfb
}

# Run main function
main "$@"
